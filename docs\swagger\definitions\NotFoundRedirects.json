{"type": "object", "properties": {"baseUrlRedirect": {"type": ["string", "null"], "description": "URL to redirect to when a user hits the domain's base URL"}, "regular404Redirect": {"type": ["string", "null"], "description": "URL to redirect to when a user hits a not found URL other than an invalid short URL"}, "invalidShortUrlRedirect": {"type": ["string", "null"], "description": "URL to redirect to when a user hits an invalid short URL"}}}