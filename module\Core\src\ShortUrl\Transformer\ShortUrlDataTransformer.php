<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\ShortUrl\Transformer;

use Shl<PERSON>io\Shlink\Core\ShortUrl\Entity\ShortUrl;
use <PERSON><PERSON><PERSON>io\Shlink\Core\ShortUrl\Helper\ShortUrlStringifierInterface;
use S<PERSON><PERSON>io\Shlink\Core\ShortUrl\Model\ShortUrlIdentifier;
use Shl<PERSON>io\Shlink\Core\ShortUrl\Model\ShortUrlWithDeps;

readonly class ShortUrlDataTransformer implements ShortUrlDataTransformerInterface
{
    public function __construct(private ShortUrlStringifierInterface $stringifier)
    {
    }

    public function transform(ShortUrlWithDeps|ShortUrl $shortUrl): array
    {
        $shortUrlIdentifier = $shortUrl instanceof ShortUrl
            ? ShortUrlIdentifier::fromShortUrl($shortUrl)
            : $shortUrl->toIdentifier();
        return [
            'shortUrl' => $this->stringifier->stringify($shortUrlIdentifier),
            ...$shortUrl->toArray(),
        ];
    }
}
