<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\ShortUrl;

use Shl<PERSON>io\Shlink\Common\Paginator\Paginator;
use Shl<PERSON>io\Shlink\Core\ShortUrl\Model\ShortUrlsParams;
use S<PERSON><PERSON>io\Shlink\Core\ShortUrl\Model\ShortUrlWithDeps;
use Shlinkio\Shlink\Rest\Entity\ApiKey;

interface ShortUrlListServiceInterface
{
    /**
     * @return Paginator<ShortUrlWithDeps>
     */
    public function listShortUrls(ShortUrlsParams $params, ApiKey|null $apiKey = null): Paginator;
}
