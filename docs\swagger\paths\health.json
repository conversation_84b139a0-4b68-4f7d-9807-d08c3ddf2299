{"get": {"operationId": "health", "tags": ["Monitoring"], "summary": "Check healthiness", "description": "Checks the healthiness of the service, making sure it can access required resources.", "responses": {"200": {"description": "The passing health status", "content": {"application/json": {"schema": {"$ref": "../definitions/Health.json"}, "example": {"status": "pass", "version": "2.10.0", "links": {"about": "https://shlink.io", "project": "https://github.com/shlinkio/shlink"}}}}}, "503": {"description": "The failing health status", "content": {"application/json": {"schema": {"$ref": "../definitions/Health.json"}, "example": {"status": "fail", "version": "2.10.0", "links": {"about": "https://shlink.io", "project": "https://github.com/shlinkio/shlink"}}}}}, "default": {"description": "Unexpected error.", "content": {"application/json": {"schema": {"$ref": "../definitions/Error.json"}}}}}}}