<?php

declare(strict_types=1);

namespace S<PERSON><PERSON><PERSON>\Shlink\CLI\Command\Db;

use S<PERSON><PERSON>io\Shlink\CLI\Command\Util\AbstractLockedCommand;
use S<PERSON>inkio\Shlink\CLI\Command\Util\LockedCommandConfig;
use S<PERSON><PERSON><PERSON>\Shlink\CLI\Util\ProcessRunnerInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Lock\LockFactory;
use Symfony\Component\Process\PhpExecutableFinder;

abstract class AbstractDatabaseCommand extends AbstractLockedCommand
{
    private string $phpBinary;

    public function __construct(
        LockFactory $locker,
        private readonly ProcessRunnerInterface $processRunner,
        PhpExecutableFinder $phpFinder,
    ) {
        parent::__construct($locker);
        $this->phpBinary = $phpFinder->find(false) ?: 'php';
    }

    protected function runPhpCommand(OutputInterface $output, array $command): void
    {
        $command = [$this->phpBinary, ...$command, '--no-interaction'];
        $this->processRunner->run($output, $command);
    }

    protected function getLockConfig(): LockedCommandConfig
    {
        return LockedCommandConfig::blocking($this->getName() ?? static::class);
    }
}
