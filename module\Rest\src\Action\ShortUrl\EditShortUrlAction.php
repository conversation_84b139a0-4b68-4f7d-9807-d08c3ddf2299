<?php

declare(strict_types=1);

namespace S<PERSON><PERSON><PERSON>\Shlink\Rest\Action\ShortUrl;

use <PERSON><PERSON>\Diactoros\Response\JsonResponse;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Shl<PERSON>io\Shlink\Core\ShortUrl\Model\ShortUrlEdition;
use Shlinkio\Shlink\Core\ShortUrl\Model\ShortUrlIdentifier;
use Shlinkio\Shlink\Core\ShortUrl\ShortUrlServiceInterface;
use Shlinkio\Shlink\Core\ShortUrl\Transformer\ShortUrlDataTransformerInterface;
use Shlinkio\Shlink\Rest\Action\AbstractRestAction;
use Shl<PERSON>io\Shlink\Rest\Middleware\AuthenticationMiddleware;

class EditShortUrlAction extends AbstractRestAction
{
    protected const string ROUTE_PATH = '/short-urls/{shortCode}';
    protected const array ROUTE_ALLOWED_METHODS = [self::METHOD_PATCH];

    public function __construct(
        private readonly ShortUrlServiceInterface $shortUrlService,
        private readonly ShortUrlDataTransformerInterface $transformer,
    ) {
    }

    public function handle(ServerRequestInterface $request): ResponseInterface
    {
        $shortUrlEdit = ShortUrlEdition::fromRawData((array) $request->getParsedBody());
        $identifier = ShortUrlIdentifier::fromApiRequest($request);
        $apiKey = AuthenticationMiddleware::apiKeyFromRequest($request);

        $shortUrl = $this->shortUrlService->updateShortUrl($identifier, $shortUrlEdit, $apiKey);

        return new JsonResponse($this->transformer->transform($shortUrl));
    }
}
