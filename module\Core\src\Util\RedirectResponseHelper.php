<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>io\Shlink\Core\Util;

use Laminas\Diactoros\Response\RedirectResponse;
use Psr\Http\Message\ResponseInterface;
use Shlinkio\Shlink\Core\Config\Options\RedirectOptions;

use function sprintf;

readonly class RedirectResponseHelper implements RedirectResponseHelperInterface
{
    public function __construct(private RedirectOptions $options)
    {
    }

    public function buildRedirectResponse(string $location): ResponseInterface
    {
        $statusCode = $this->options->redirectStatusCode;
        $headers = ! $statusCode->allowsCache() ? [] : [
            'Cache-Control' => sprintf('private,max-age=%s', $this->options->redirectCacheLifetime),
        ];

        return new RedirectResponse($location, $statusCode->value, $headers);
    }
}
