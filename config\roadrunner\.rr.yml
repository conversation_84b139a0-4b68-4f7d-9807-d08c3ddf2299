version: '3'

rpc:
  listen: tcp://127.0.0.1:6001

server:
  command: 'php -dopcache.enable_cli=1 -dopcache.validate_timestamps=0 ../../bin/roadrunner-worker.php'

http:
  address: '${ADDRESS:-0.0.0.0}:${PORT:-8080}'
  middleware: ['static']
  static:
    dir: '../../public'
    forbid: ['.php', '.htaccess']
  pool:
    num_workers: ${WEB_WORKER_NUM:-0}

jobs:
  timeout: 300 # 5 minutes
  pool:
    num_workers: ${TASK_WORKER_NUM:-0}
  consume: ['shlink']
  pipelines:
    shlink:
      driver: memory
      config:
        priority: 10
        prefetch: 10

logs:
  mode: production
  channels:
    http:
      mode: 'off' # Disable logging as Shlink handles it internally
    server:
      level: info
    jobs:
      level: debug
