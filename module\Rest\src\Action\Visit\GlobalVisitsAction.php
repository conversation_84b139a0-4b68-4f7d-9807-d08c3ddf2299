<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Rest\Action\Visit;

use Lam<PERSON>\Diactoros\Response\JsonResponse;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use <PERSON><PERSON><PERSON>io\Shlink\Core\Visit\VisitsStatsHelperInterface;
use Shlinkio\Shlink\Rest\Action\AbstractRestAction;
use Shlinkio\Shlink\Rest\Middleware\AuthenticationMiddleware;

class GlobalVisitsAction extends AbstractRestAction
{
    protected const string ROUTE_PATH = '/visits';
    protected const array ROUTE_ALLOWED_METHODS = [self::METHOD_GET];

    public function __construct(private readonly VisitsStatsHelperInterface $statsHelper)
    {
    }

    public function handle(ServerRequestInterface $request): ResponseInterface
    {
        $apiKey = AuthenticationMiddleware::apiKeyFromRequest($request);

        return new JsonResponse([
            'visits' => $this->statsHelper->getVisitsStats($apiKey),
        ]);
    }
}
