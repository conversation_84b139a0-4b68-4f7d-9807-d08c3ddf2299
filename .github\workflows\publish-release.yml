name: Publish release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        php-version: ['8.3', '8.4']
    steps:
      - uses: actions/checkout@v4
      - uses: './.github/actions/ci-setup'
        with:
          php-version: ${{ matrix.php-version }}
          extensions-cache-key: publish-swagger-spec-extensions-${{ matrix.php-version }}
          install-deps: 'no'
      - run: ./build.sh ${GITHUB_REF#refs/tags/v}
      - uses: actions/upload-artifact@v4
        with:
          name: dist-files-${{ matrix.php-version }}
          path: build

  publish:
    needs: ['build']
    runs-on: ubuntu-24.04
    steps:
      - uses: actions/checkout@v4
      - uses: actions/download-artifact@v4
        with:
          path: build
      - name: Publish release with assets
        uses: docker://antony<PERSON>chenko/git-release:latest
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          ALLOW_EMPTY_CHANGELOG: "true"
        with:
          args: |
            build/*/shlink*_dist.zip

  delete-artifacts:
    needs: ['publish']
    runs-on: ubuntu-24.04
    steps:
      - uses: geekyeggo/delete-artifact@v5
        with:
          name: dist-files-*
