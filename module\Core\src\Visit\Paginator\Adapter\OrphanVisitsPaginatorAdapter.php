<?php

declare(strict_types=1);

namespace S<PERSON><PERSON><PERSON>\Shlink\Core\Visit\Paginator\Adapter;

use Shl<PERSON>io\Shlink\Core\Paginator\Adapter\AbstractCacheableCountPaginatorAdapter;
use S<PERSON>inkio\Shlink\Core\Visit\Entity\Visit;
use Shlinkio\Shlink\Core\Visit\Model\OrphanVisitsParams;
use Shlinkio\Shlink\Core\Visit\Persistence\OrphanVisitsCountFiltering;
use Shlinkio\Shlink\Core\Visit\Persistence\OrphanVisitsListFiltering;
use Shlinkio\Shlink\Core\Visit\Repository\VisitRepositoryInterface;
use Shl<PERSON>io\Shlink\Rest\Entity\ApiKey;

/** @extends AbstractCacheableCountPaginatorAdapter<Visit> */
class OrphanVisitsPaginatorAdapter extends AbstractCacheableCountPaginatorAdapter
{
    public function __construct(
        private readonly VisitRepositoryInterface $repo,
        private readonly OrphanVisitsParams $params,
        private readonly ApiKey|null $apiKey,
    ) {
    }

    protected function doCount(): int
    {
        return $this->repo->countOrphanVisits(new OrphanVisitsCountFiltering(
            dateRange: $this->params->dateRange,
            excludeBots: $this->params->excludeBots,
            apiKey: $this->apiKey,
            type: $this->params->type,
        ));
    }

    public function getSlice(int $offset, int $length): iterable
    {
        return $this->repo->findOrphanVisits(new OrphanVisitsListFiltering(
            dateRange: $this->params->dateRange,
            excludeBots: $this->params->excludeBots,
            apiKey: $this->apiKey,
            type: $this->params->type,
            limit: $length,
            offset: $offset,
        ));
    }
}
