<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\ShortUrl;

use Shlinkio\Shlink\Core\Exception\NonUniqueSlugException;
use Shlinkio\Shlink\Core\ShortUrl\Model\ShortUrlCreation;
use S<PERSON>inkio\Shlink\Core\ShortUrl\Model\UrlShorteningResult;

interface UrlShortenerInterface
{
    /**
     * @throws NonUniqueSlugException
     */
    public function shorten(ShortUrlCreation $creation): UrlShorteningResult;
}
