name: Tests

on:
  workflow_call:
    inputs:
      test-group:
        type: string
        required: true
        description: One of unit, api or cli

jobs:
  tests:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        php-version: ['8.3', '8.4']
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # rr get-binary picks this env automatically
    steps:
      - uses: actions/checkout@v4
      - name: Start postgres database server
        if: ${{ inputs.test-group == 'api' }}
        run: docker compose -f docker-compose.yml -f docker-compose.ci.yml up -d shlink_db_postgres
      - name: Start maria database server
        if: ${{ inputs.test-group == 'cli' }}
        run: docker compose -f docker-compose.yml -f docker-compose.ci.yml up -d shlink_db_maria
      - uses: './.github/actions/ci-setup'
        with:
          php-version: ${{ matrix.php-version }}
          extensions-cache-key: tests-extensions-${{ matrix.php-version }}-${{ inputs.test-group }}
      - name: Download RoadRunner binary
        if: ${{ inputs.test-group == 'api' }}
        run: ./vendor/bin/rr get --no-interaction --no-config --location bin/ && chmod +x bin/rr
      - run: composer test:${{ inputs.test-group }}:ci
      - uses: actions/upload-artifact@v4
        if: ${{ matrix.php-version == '8.3' }}
        with:
          name: coverage-${{ inputs.test-group }}
          path: |
            build/coverage-${{ inputs.test-group }}
            build/coverage-${{ inputs.test-group }}.cov
