{"get": {"operationId": "getGlobalVisits", "tags": ["Visits"], "summary": "Get general visits stats", "description": "Get general visits stats not linked to one specific short URL.", "parameters": [{"$ref": "../parameters/version.json"}], "security": [{"ApiKey": []}], "responses": {"200": {"description": "Visits stats.", "content": {"application/json": {"schema": {"type": "object", "properties": {"visits": {"$ref": "../definitions/VisitStats.json"}}}, "example": {"visits": {"nonOrphanVisits": {"total": 64994, "nonBots": 64986, "bots": 8}, "orphanVisits": {"total": 37, "nonBots": 34, "bots": 3}}}}}}, "default": {"description": "Unexpected error.", "content": {"application/problem+json": {"schema": {"$ref": "../definitions/Error.json"}}}}}}}