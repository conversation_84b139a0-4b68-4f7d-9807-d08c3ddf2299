name: Continuous integration

on:
  pull_request:
    paths-ignore:
      - 'LICENSE'
      - '.*'
      - '*.md'
      - '*.xml'
      - '*.yml*'
      - '*.neon'
  push:
    branches:
      - main
      - develop
      - 2.x
    paths-ignore:
      - 'LICENSE'
      - '.*'
      - '*.md'
      - '*.xml'
      - '*.yml*'
      - '*.neon'

jobs:
  static-analysis:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        php-version: ['8.3']
        command: ['cs', 'stan', 'openapi:validate']
    steps:
      - uses: actions/checkout@v4
      - uses: './.github/actions/ci-setup'
        with:
          php-version: ${{ matrix.php-version }}
          extensions-cache-key: tests-extensions-${{ matrix.php-version }}-${{ matrix.command }}
      - run: composer ${{ matrix.command }}

  unit-tests:
    uses: './.github/workflows/ci-tests.yml'
    with:
      test-group: unit

  cli-tests:
    uses: './.github/workflows/ci-tests.yml'
    with:
      test-group: cli

  api-tests:
    uses: './.github/workflows/ci-tests.yml'
    with:
      test-group: api

  db-tests:
    strategy:
      matrix:
        platform: ['sqlite:ci', 'mysql', 'maria', 'postgres', 'ms']
    uses: './.github/workflows/ci-db-tests.yml'
    with:
      platform: ${{ matrix.platform }}

  upload-coverage:
    needs:
      - unit-tests
      - api-tests
      - cli-tests
      - db-tests
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        php-version: ['8.3']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Use PHP
        uses: './.github/actions/ci-setup'
        with:
          php-version: ${{ matrix.php-version }}
          extensions-cache-key: tests-extensions-${{ matrix.php-version }}
      - uses: actions/download-artifact@v4
        with:
          path: build
      - run: mv build/coverage-unit/coverage-unit.cov build/coverage-unit.cov
      - run: mv build/coverage-db/coverage-db.cov build/coverage-db.cov
      - run: mv build/coverage-api/coverage-api.cov build/coverage-api.cov
      - run: mv build/coverage-cli/coverage-cli.cov build/coverage-cli.cov
      - run: vendor/bin/phpcov merge build --clover build/clover.xml
      - name: Publish coverage
        uses: codecov/codecov-action@v4
        with:
          file: ./build/clover.xml

  delete-artifacts:
    needs:
      - upload-coverage
    runs-on: ubuntu-24.04
    steps:
      - uses: geekyeggo/delete-artifact@v5
        with:
          name: |
            coverage-*
