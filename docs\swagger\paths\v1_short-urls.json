{"get": {"operationId": "listShortUrls", "tags": ["Short URLs"], "summary": "List short URLs", "description": "Returns the list of short URLs.", "parameters": [{"$ref": "../parameters/version.json"}, {"name": "page", "in": "query", "description": "The page to be displayed. Defaults to 1", "required": false, "schema": {"type": "integer"}}, {"name": "itemsPerPage", "in": "query", "description": "The amount of items to return on every page. Defaults to 10", "required": false, "schema": {"type": "number"}}, {"name": "searchTerm", "in": "query", "description": "A query used to filter results by searching for it on the longUrl and shortCode fields. (Since v1.3.0)", "required": false, "schema": {"type": "string"}}, {"name": "tags[]", "in": "query", "description": "A list of tags used to filter the result set. Only short URLs tagged with at least one of the provided tags will be returned. (Since v1.3.0)", "required": false, "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "tagsMode", "in": "query", "description": "Tells how the filtering by tags should work, returning short URLs containing \"any\" of the tags, or \"all\" the tags. It's ignored if no tags are provided, and defaults to \"any\" if not provided.", "required": false, "schema": {"type": "string", "enum": ["any", "all"]}}, {"name": "orderBy", "in": "query", "description": "The field from which you want to order the result.", "required": false, "schema": {"type": "string", "enum": ["longUrl-ASC", "longUrl-DESC", "shortCode-ASC", "shortCode-DESC", "dateCreated-ASC", "dateCreated-DESC", "title-ASC", "title-DESC", "visits-ASC", "visits-DESC", "nonBotVisits-ASC", "nonBotVisits-DESC"]}}, {"name": "startDate", "in": "query", "description": "The date (in ISO-8601 format) from which we want to get short URLs.", "required": false, "schema": {"type": "string"}}, {"name": "endDate", "in": "query", "description": "The date (in ISO-8601 format) until which we want to get short URLs.", "required": false, "schema": {"type": "string"}}, {"name": "excludeMaxVisitsReached", "in": "query", "description": "If true, short URLs which already reached their maximum amount of visits will be excluded.", "required": false, "schema": {"type": "string", "enum": ["true", "false"]}}, {"name": "excludePastValidUntil", "in": "query", "description": "If true, short URLs which validUntil date is on the past will be excluded.", "required": false, "schema": {"type": "string", "enum": ["true", "false"]}}, {"name": "domain", "in": "query", "description": "Get short URLs for this particular domain only. Use **DEFAULT** keyword for default domain.", "required": false, "schema": {"type": "string"}}], "security": [{"ApiKey": []}], "responses": {"200": {"description": "The list of short URLs", "content": {"application/json": {"schema": {"type": "object", "properties": {"shortUrls": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "../definitions/ShortUrl.json"}}, "pagination": {"$ref": "../definitions/Pagination.json"}}}}}, "example": {"shortUrls": {"data": [{"shortCode": "12C18", "shortUrl": "https://s.test/12C18", "longUrl": "https://store.steampowered.com", "dateCreated": "2016-08-21T20:34:16+02:00", "visitsSummary": {"total": 328, "nonBots": 328, "bots": 0}, "tags": ["games", "tech"], "meta": {"validSince": "2017-01-21T00:00:00+02:00", "validUntil": null, "maxVisits": 100}, "domain": null, "title": "Welcome to Steam", "crawlable": false, "forwardQuery": true, "hasRedirectRules": true}, {"shortCode": "12Kb3", "shortUrl": "https://s.test/12Kb3", "longUrl": "https://shlink.io", "dateCreated": "2016-05-01T20:34:16+02:00", "visitsSummary": {"total": 1029, "nonBots": 900, "bots": 129}, "tags": ["shlink"], "meta": {"validSince": null, "validUntil": null, "maxVisits": null}, "domain": null, "title": null, "crawlable": false, "forwardQuery": true, "hasRedirectRules": false}, {"shortCode": "123bA", "shortUrl": "https://example.com/123bA", "longUrl": "https://www.google.com", "dateCreated": "2015-10-01T20:34:16+02:00", "visitsSummary": {"total": 25, "nonBots": 0, "bots": 25}, "tags": [], "meta": {"validSince": "2017-01-21T00:00:00+02:00", "validUntil": null, "maxVisits": null}, "domain": "example.com", "title": null, "crawlable": false, "forwardQuery": false, "hasRedirectRules": true}], "pagination": {"currentPage": 5, "pagesCount": 12, "itemsPerPage": 10, "itemsInCurrentPage": 10, "totalItems": 115}}}}}}, "default": {"description": "Unexpected error.", "content": {"application/problem+json": {"schema": {"$ref": "../definitions/Error.json"}}}}}}, "post": {"operationId": "createShortUrl", "tags": ["Short URLs"], "summary": "Create short URL", "description": "Creates a new short URL.<br></br>**Param findIfExists**: This new param allows to force shlink to return existing short URLs when found based on provided params, instead of creating a new one. However, it might add complexity and have unexpected outputs.\n\nThese are the use cases:\n* Only the long URL is provided: It will return the newest match or create a new short URL if none is found.\n* Long url and custom slug are provided: It will return the short URL when both params match, return an error when the slug is in use for another long URL, or create a new short URL otherwise.\n* Any of the above but including other params (tags, validSince, validUntil, maxVisits): It will behave the same as the previous two cases, but it will try to exactly match existing results using all the params. If any of them does not match, it will try to create a new short URL.", "security": [{"ApiKey": []}], "parameters": [{"$ref": "../parameters/version.json"}], "requestBody": {"description": "Request body.", "required": true, "content": {"application/json": {"schema": {"allOf": [{"$ref": "../definitions/ShortUrlEdition.json"}, {"type": "object", "required": ["longUrl"], "properties": {"customSlug": {"description": "A unique custom slug to be used instead of the generated short code", "type": "string"}, "pathPrefix": {"description": "A prefix that will be prepended to provided custom slug or auto-generated short code", "type": "string"}, "findIfExists": {"description": "Will force existing matching URL to be returned if found, instead of creating a new one", "type": "boolean"}, "domain": {"description": "The domain to which the short URL will be attached", "type": "string"}, "shortCodeLength": {"description": "The length for generated short code. It has to be at least 4 and defaults to 5. It will be ignored when customSlug is provided", "type": "number"}}}]}}}}, "responses": {"200": {"description": "The result of parsing the long URL", "content": {"application/json": {"schema": {"$ref": "../definitions/ShortUrl.json"}, "example": {"shortCode": "12C18", "shortUrl": "https://s.test/12C18", "longUrl": "https://store.steampowered.com", "dateCreated": "2016-08-21T20:34:16+02:00", "visitsSummary": {"total": 0, "nonBots": 0, "bots": 0}, "tags": ["games", "tech"], "meta": {"validSince": "2017-01-21T00:00:00+02:00", "validUntil": null, "maxVisits": 500}, "domain": null, "title": null, "crawlable": false, "forwardQuery": true, "hasRedirectRules": false}}}}, "400": {"description": "Some of provided data is invalid. Check extra fields to know exactly what.", "content": {"application/problem+json": {"schema": {"type": "object", "allOf": [{"$ref": "../definitions/Error.json"}, {"type": "object", "properties": {"invalidElements": {"type": "array", "items": {"type": "string", "enum": ["validSince", "validUntil", "customSlug", "pathPrefix", "maxVisits", "findIfExists", "domain"]}}, "customSlug": {"type": "string", "description": "Provided custom slug when the error type is https://shlink.io/api/error/non-unique-slug"}, "domain": {"type": "string", "description": "The domain for which you were trying to create the new short URL"}}}]}, "examples": {"Invalid arguments": {"$ref": "../examples/short-url-invalid-args-v3.json"}, "Non-unique slug": {"value": {"title": "Invalid custom slug", "type": "https://shlink.io/api/error/non-unique-slug", "detail": "Provided slug \"my-slug\" is already in use.", "status": 400, "customSlug": "my-slug"}}}}}}, "default": {"description": "Unexpected error.", "content": {"application/problem+json": {"schema": {"$ref": "../definitions/Error.json"}}}}}}}