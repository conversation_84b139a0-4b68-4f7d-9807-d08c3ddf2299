{"get": {"operationId": "getDomainVisits", "tags": ["Visits"], "summary": "List visits for domain", "description": "Get the list of visits on any short URL which belongs to provided domain.", "parameters": [{"$ref": "../parameters/version.json"}, {"name": "domain", "in": "path", "description": "The domain from which we want to get the visits, or **DEFAULT** keyword for default domain.", "required": true, "schema": {"type": "string"}}, {"name": "startDate", "in": "query", "description": "The date (in ISO-8601 format) from which we want to get visits.", "required": false, "schema": {"type": "string"}}, {"name": "endDate", "in": "query", "description": "The date (in ISO-8601 format) until which we want to get visits.", "required": false, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "The page to display. Defaults to 1", "required": false, "schema": {"type": "number"}}, {"name": "itemsPerPage", "in": "query", "description": "The amount of items to return on every page. Defaults to all the items", "required": false, "schema": {"type": "number"}}, {"name": "excludeBots", "in": "query", "description": "Tells if visits from potential bots should be excluded from the result set", "required": false, "schema": {"type": "string", "enum": ["true"]}}], "security": [{"ApiKey": []}], "responses": {"200": {"description": "List of visits.", "content": {"application/json": {"schema": {"type": "object", "properties": {"visits": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "../definitions/Visit.json"}}, "pagination": {"$ref": "../definitions/Pagination.json"}}}}}, "example": {"visits": {"data": [{"referer": "https://twitter.com", "date": "2015-08-20T05:05:03+04:00", "userAgent": "Mozilla/5.0 (Windows NT 6.1; Win64; x64; rv:47.0) Gecko/20100101 Firefox/47.0 Mozilla/5.0 (Macintosh; Intel Mac OS X x.y; rv:42.0) Gecko/20100101 Firefox/42.0", "visitLocation": null, "potentialBot": false, "visitedUrl": "https://s.test"}, {"referer": "https://t.co", "date": "2015-08-20T05:05:03+04:00", "userAgent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/51.0.2704.103 Safari/537.36", "visitLocation": {"cityName": "Cupertino", "countryCode": "US", "countryName": "United States", "latitude": 37.3042, "longitude": -122.0946, "regionName": "California", "timezone": "America/Los_Angeles"}, "potentialBot": false, "visitedUrl": "https://s.test"}, {"referer": null, "date": "2015-08-20T05:05:03+04:00", "userAgent": "some_web_crawler/1.4", "visitLocation": null, "potentialBot": true, "visitedUrl": "https://s.test"}], "pagination": {"currentPage": 5, "pagesCount": 12, "itemsPerPage": 10, "itemsInCurrentPage": 10, "totalItems": 115}}}}}}, "404": {"description": "The domain does not exist.", "content": {"application/problem+json": {"schema": {"$ref": "../definitions/Error.json"}, "examples": {"API v3 and newer": {"value": {"detail": "Domain with authority \"example.com\" could not be found", "title": "Domain not found", "type": "https://shlink.io/api/error/domain-not-found", "status": 404, "authority": "example.com"}}, "Previous to API v3": {"value": {"detail": "Domain with authority \"example.com\" could not be found", "title": "Domain not found", "type": "DOMAIN_NOT_FOUND", "status": 404, "authority": "example.com"}}}}}}, "default": {"description": "Unexpected error.", "content": {"application/problem+json": {"schema": {"$ref": "../definitions/Error.json"}}}}}}}