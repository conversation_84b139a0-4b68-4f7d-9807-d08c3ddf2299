<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Rest\Action\Visit;

use Pagerfanta\Pagerfanta;
use Psr\Http\Message\ServerRequestInterface as Request;
use Shlinkio\Shlink\Core\ShortUrl\Model\ShortUrlIdentifier;
use Shl<PERSON>io\Shlink\Core\Visit\Model\VisitsParams;
use Shlinkio\Shlink\Rest\Entity\ApiKey;

class ShortUrlVisitsAction extends AbstractListVisitsAction
{
    protected const string ROUTE_PATH = '/short-urls/{shortCode}/visits';

    protected function getVisitsPaginator(Request $request, VisitsParams $params, ApiKey $apiKey): Pagerfanta
    {
        $identifier = ShortUrlIdentifier::fromApiRequest($request);
        return $this->visitsHelper->visitsForShortUrl($identifier, $params, $apiKey);
    }
}
