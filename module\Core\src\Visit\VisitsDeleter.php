<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\Visit;

use Shl<PERSON>io\Shlink\Core\Model\BulkDeleteResult;
use Shl<PERSON>io\Shlink\Core\Visit\Repository\VisitDeleterRepositoryInterface;
use Shl<PERSON>io\Shlink\Rest\ApiKey\Role;
use Shlinkio\Shlink\Rest\Entity\ApiKey;

readonly class VisitsDeleter implements VisitsDeleterInterface
{
    public function __construct(private VisitDeleterRepositoryInterface $repository)
    {
    }

    public function deleteOrphanVisits(ApiKey|null $apiKey = null): BulkDeleteResult
    {
        $affectedItems = $apiKey?->hasRole(Role::NO_ORPHAN_VISITS) ? 0 : $this->repository->deleteOrphanVisits();
        return new BulkDeleteResult($affectedItems);
    }
}
