<?php

declare(strict_types=1);

namespace Shl<PERSON><PERSON>\Shlink\Core\ErrorHandler;

use Closure;
use Fig\Http\Message\StatusCodeInterface;
use <PERSON>inas\Diactoros\Response;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use Psr\Http\Server\RequestHandlerInterface;
use Shlinkio\Shlink\Core\ErrorHandler\Model\NotFoundType;

use function file_get_contents;
use function sprintf;

class NotFoundTemplateHandler implements RequestHandlerInterface
{
    private const string TEMPLATES_BASE_DIR = __DIR__ . '/../../templates';
    public const string NOT_FOUND_TEMPLATE = '404.html';
    public const string INVALID_SHORT_CODE_TEMPLATE = 'invalid-short-code.html';

    private Closure $readFile;

    public function __construct(callable|null $readFile = null)
    {
        $this->readFile = $readFile ? Closure::fromCallable($readFile) : fn (string $file) => file_get_contents($file);
    }

    public function handle(ServerRequestInterface $request): ResponseInterface
    {
        /** @var NotFoundType $notFoundType */
        $notFoundType = $request->getAttribute(NotFoundType::class);
        $status = StatusCodeInterface::STATUS_NOT_FOUND;

        $template = $notFoundType->isInvalidShortUrl() ? self::INVALID_SHORT_CODE_TEMPLATE : self::NOT_FOUND_TEMPLATE;
        $templateContent = ($this->readFile)(sprintf('%s/%s', self::TEMPLATES_BASE_DIR, $template));
        return new Response\HtmlResponse($templateContent, $status);
    }
}
