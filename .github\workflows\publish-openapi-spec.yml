name: Publish openapi spec

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        php-version: ['8.3']
    steps:
      - uses: actions/checkout@v4
      - name: Determine version
        id: determine_version
        run: echo "version=${GITHUB_REF#refs/tags/}" >> $GITHUB_OUTPUT
        shell: bash
      - uses: './.github/actions/ci-setup'
        with:
          php-version: ${{ matrix.php-version }}
          extensions-cache-key: publish-openapi-spec-extensions-${{ matrix.php-version }}
      - run: composer openapi:inline
      - run: mkdir ${{ steps.determine_version.outputs.version }}
      - run: mv docs/swagger/openapi-inlined.json ${{ steps.determine_version.outputs.version }}/open-api-spec.json
      - name: Publish spec
        uses: JamesIves/github-pages-deploy-action@v4
        with:
          token: ${{ secrets.OAS_PUBLISH_TOKEN }}
          repository-name: 'shlinkio/shlink-open-api-specs'
          branch: main
          folder: ${{ steps.determine_version.outputs.version }}
          target-folder: specs/${{ steps.determine_version.outputs.version }}
          clean: false
