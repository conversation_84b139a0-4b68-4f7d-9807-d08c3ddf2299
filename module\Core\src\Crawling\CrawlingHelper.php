<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON>io\Shlink\Core\Crawling;

use <PERSON>hlinkio\Shlink\Core\ShortUrl\Repository\CrawlableShortCodesQueryInterface;

readonly class CrawlingHelper implements CrawlingHelperInterface
{
    public function __construct(private CrawlableShortCodesQueryInterface $query)
    {
    }

    public function listCrawlableShortCodes(): iterable
    {
        yield from ($this->query)();
    }
}
