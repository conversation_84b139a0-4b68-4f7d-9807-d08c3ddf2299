name: Database tests

on:
  workflow_call:
    inputs:
      platform:
        type: string
        required: true
        description: One of sqlite:ci, mysql, maria, postgres or ms

jobs:
  db-tests:
    runs-on: ubuntu-24.04
    strategy:
      matrix:
        php-version: ['8.3', '8.4']
    env:
      LC_ALL: C
    steps:
      - uses: actions/checkout@v4
      - name: Install MSSQL ODBC
        if: ${{ inputs.platform == 'ms' }}
        run: sudo ./data/infra/ci/install-ms-odbc.sh
      - name: Start database server
        if: ${{ inputs.platform != 'sqlite:ci' }}
        run: docker compose -f docker-compose.yml -f docker-compose.ci.yml up -d shlink_db_${{ inputs.platform }}
      - uses: './.github/actions/ci-setup'
        with:
          php-version: ${{ matrix.php-version }}
          php-extensions: pdo_sqlsrv-5.12.0
          extensions-cache-key: db-tests-extensions-${{ matrix.php-version }}-${{ inputs.platform }}
      - name: Create test database
        if: ${{ inputs.platform == 'ms' }}
        run: docker compose exec -T shlink_db_ms /opt/mssql-tools18/bin/sqlcmd -C -S localhost -U sa -P 'Passw0rd!' -Q "CREATE DATABASE shlink_test;"
      - name: Run tests
        run: composer test:db:${{ inputs.platform }}
      - name: Upload code coverage
        uses: actions/upload-artifact@v4
        if: ${{ matrix.php-version == '8.3' && inputs.platform == 'sqlite:ci' }}
        with:
          name: coverage-db
          path: |
            build/coverage-db
            build/coverage-db.cov
