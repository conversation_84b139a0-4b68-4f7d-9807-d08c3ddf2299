name: Build and publish docker image

on:
  push:
    tags:
      - 'v*'

jobs:
  build-image:
    strategy:
      matrix:
        include:
          - runtime: 'rr'
            platforms: 'linux/arm64/v8,linux/amd64'
          - runtime: 'rr'
            tag-suffix: 'roadrunner'
            platforms: 'linux/arm64/v8,linux/amd64'
    uses: shlinkio/github-actions/.github/workflows/docker-publish-image.yml@main
    secrets: inherit
    with:
      image-name: shlinkio/shlink
      version-arg-name: SHLINK_VERSION
      platforms: ${{ matrix.platforms }}
      tags-suffix: ${{ matrix.tag-suffix }}
      extra-build-args: |
        SHLINK_RUNTIME=${{ matrix.runtime }}
