<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\ShortUrl;

use Shl<PERSON>io\Shlink\Core\Exception\ShortUrlNotFoundException;
use Shl<PERSON>io\Shlink\Core\Model\BulkDeleteResult;
use Shl<PERSON>io\Shlink\Core\ShortUrl\Model\ShortUrlIdentifier;
use Shlinkio\Shlink\Core\Visit\Repository\VisitDeleterRepositoryInterface;
use Shlinkio\Shlink\Rest\Entity\ApiKey;

readonly class ShortUrlVisitsDeleter implements ShortUrlVisitsDeleterInterface
{
    public function __construct(
        private VisitDeleterRepositoryInterface $repository,
        private ShortUrlResolverInterface $resolver,
    ) {
    }

    /**
     * @throws ShortUrlNotFoundException
     */
    public function deleteShortUrlVisits(ShortUrlIdentifier $identifier, ApiKey|null $apiKey = null): BulkDeleteResult
    {
        $shortUrl = $this->resolver->resolveShortUrl($identifier, $apiKey);
        return new BulkDeleteResult($this->repository->deleteShortUrlVisits($shortUrl));
    }
}
