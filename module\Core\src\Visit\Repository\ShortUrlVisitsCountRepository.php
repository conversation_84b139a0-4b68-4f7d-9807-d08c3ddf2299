<?php

declare(strict_types=1);

namespace S<PERSON><PERSON><PERSON>\Shlink\Core\Visit\Repository;

use Happyr\DoctrineSpecification\Repository\EntitySpecificationRepository;
use Shlinkio\Shlink\Core\ShortUrl\Entity\ShortUrl;
use Shl<PERSON>io\Shlink\Core\Visit\Entity\ShortUrlVisitsCount;
use Shlinkio\Shlink\Core\Visit\Persistence\VisitsCountFiltering;

/** @extends EntitySpecificationRepository<ShortUrl> */
class ShortUrlVisitsCountRepository extends EntitySpecificationRepository implements
    ShortUrlVisitsCountRepositoryInterface
{
    public function countNonOrphanVisits(VisitsCountFiltering $filtering): int
    {
        $qb = $this->getEntityManager()->createQueryBuilder();
        $qb->select('COALESCE(SUM(vc.count), 0)')
           ->from(ShortUrlVisitsCount::class, 'vc')
           ->join('vc.shortUrl', 's');


        if ($filtering->excludeBots) {
            $qb->andWhere($qb->expr()->eq('vc.potentialBot', ':potentialBot'))
               ->setParameter('potentialBot', false);
        }

        $this->applySpecification($qb, $filtering->apiKey?->spec(), 's');

        return (int) $qb->getQuery()->getSingleScalarResult();
    }
}
