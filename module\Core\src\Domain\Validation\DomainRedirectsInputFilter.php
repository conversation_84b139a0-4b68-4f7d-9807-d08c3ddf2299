<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\Domain\Validation;

use <PERSON><PERSON>\InputFilter\InputFilter;
use Shl<PERSON>io\Shlink\Common\Validation\HostAndPortValidator;
use S<PERSON><PERSON><PERSON>\Shlink\Common\Validation\InputFactory;

/** @extends InputFilter<mixed> */
class DomainRedirectsInputFilter extends InputFilter
{
    public const string DOMAIN = 'domain';
    public const string BASE_URL_REDIRECT = 'baseUrlRedirect';
    public const string REGULAR_404_REDIRECT = 'regular404Redirect';
    public const string INVALID_SHORT_URL_REDIRECT = 'invalidShortUrlRedirect';

    private function __construct()
    {
    }

    public static function withData(array $data): self
    {
        $instance = new self();

        $instance->initializeInputs();
        $instance->setData($data);

        return $instance;
    }

    private function initializeInputs(): void
    {
        $domain = InputFactory::basic(self::DOMAIN, required: true);
        $domain->getValidator<PERSON>hain()->attach(new HostAndPortValidator());
        $this->add($domain);

        $this->add(InputFactory::basic(self::BASE_URL_REDIRECT));
        $this->add(InputFactory::basic(self::REGULAR_404_REDIRECT));
        $this->add(InputFactory::basic(self::INVALID_SHORT_URL_REDIRECT));
    }
}
