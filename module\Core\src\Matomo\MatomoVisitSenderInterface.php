<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\Matomo;

use Shl<PERSON>io\Shlink\Common\Util\DateRange;
use Shlinkio\Shlink\Core\Matomo\Model\SendVisitsResult;
use S<PERSON><PERSON>io\Shlink\Core\Visit\Entity\Visit;

interface MatomoVisitSenderInterface
{
    /**
     * Sends all visits in provided date range to matomo, and returns the amount of affected visits
     */
    public function sendVisitsInDateRange(
        DateRange $dateRange,
        VisitSendingProgressTrackerInterface|null $progressTracker = null,
    ): SendVisitsResult;

    public function sendVisit(Visit $visit, string|null $originalIpAddress = null): void;
}
