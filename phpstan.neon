includes:
    - vendor/phpstan/phpstan-doctrine/extension.neon
    - vendor/phpstan/phpstan-symfony/extension.neon
    - vendor/phpstan/phpstan-phpunit/extension.neon
    - vendor/phpstan/phpstan-phpunit/rules.neon
parameters:
    level: 8
    paths:
        - module
        - config
        - docker/config
    symfony:
        consoleApplicationLoader: 'config/cli-app.php'
    doctrine:
        repositoryClass: Happyr\DoctrineSpecification\Repository\EntitySpecificationRepository
        objectManagerLoader: 'config/entity-manager.php'
    ignoreErrors:
        - '#should return int<0, max> but returns int#'
        - '#expects -1\|int<1, max>, int given#'
        - identifier: missingType.iterableValue
