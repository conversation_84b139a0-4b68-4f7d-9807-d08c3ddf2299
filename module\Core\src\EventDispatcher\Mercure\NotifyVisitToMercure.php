<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\EventDispatcher\Mercure;

use Shl<PERSON>io\Shlink\Core\EventDispatcher\Async\AbstractNotifyVisitListener;
use S<PERSON><PERSON>io\Shlink\Core\EventDispatcher\Async\RemoteSystem;

class NotifyVisitToMercure extends AbstractNotifyVisitListener
{
    protected function isEnabled(): bool
    {
        return true;
    }

    protected function getRemoteSystem(): RemoteSystem
    {
        return RemoteSystem::MERCURE;
    }
}
