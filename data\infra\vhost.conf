server {
    listen 80 default_server;
    server_name shlink.local;
    root /home/<USER>/www/public;
    index index.php;

    charset utf-8;
    error_log /home/<USER>/www/data/infra/nginx/shlink.error.log;

    location / {
        try_files $uri $uri/ /index.php$is_args$args;
    }

    location ~ \.php$ {
        root /home/<USER>/www/public;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass shlink_php:9000;
        fastcgi_index index.php;
        include fastcgi.conf;
    }
}
