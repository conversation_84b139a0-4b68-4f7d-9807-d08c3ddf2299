<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\EventDispatcher\Mercure;

use Shl<PERSON>io\Shlink\Core\EventDispatcher\Async\AbstractNotifyNewShortUrlListener;
use S<PERSON><PERSON><PERSON>\Shlink\Core\EventDispatcher\Async\RemoteSystem;

class NotifyNewShortUrlToMercure extends AbstractNotifyNewShortUrlListener
{
    protected function isEnabled(): bool
    {
        return true;
    }

    protected function getRemoteSystem(): RemoteSystem
    {
        return RemoteSystem::MERCURE;
    }
}
