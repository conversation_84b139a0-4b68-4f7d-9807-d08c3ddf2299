{"name": "shlinkio/shlink", "type": "project", "homepage": "https://shlink.io", "description": "A self-hosted and PHP-based URL shortener application with CLI and REST interfaces", "license": "MIT", "authors": [{"name": "<PERSON>", "homepage": "https://www.alejandrocelaya.com", "email": "<EMAIL>"}], "require": {"php": "^8.3", "ext-curl": "*", "ext-gd": "*", "ext-json": "*", "ext-mbstring": "*", "ext-pdo": "*", "akrabat/ip-address-middleware": "^2.6", "cakephp/chronos": "^3.1", "doctrine/dbal": "^4.2", "doctrine/migrations": "^3.8", "doctrine/orm": "^3.3", "donatj/phpuseragentparser": "^1.10", "endroid/qr-code": "^6.0.5", "friendsofphp/proxy-manager-lts": "^1.0", "geoip2/geoip2": "^3.1", "guzzlehttp/guzzle": "^7.9", "hidehalo/nanoid-php": "^2.0", "jaybizzle/crawler-detect": "^1.3", "laminas/laminas-config-aggregator": "^1.17", "laminas/laminas-diactoros": "^3.5", "laminas/laminas-inputfilter": "^2.31", "laminas/laminas-servicemanager": "^3.23", "laminas/laminas-stdlib": "^3.20", "matomo/matomo-php-tracker": "^3.3", "mezzio/mezzio": "^3.20", "mezzio/mezzio-fastroute": "^3.12", "mezzio/mezzio-problem-details": "^1.15", "mlocati/ip-lib": "^1.18.1", "pagerfanta/core": "^3.8", "ramsey/uuid": "^4.7", "shlinkio/doctrine-specification": "^2.2", "shlinkio/shlink-common": "dev-main#7469270 as 7.1", "shlinkio/shlink-config": "^4.0", "shlinkio/shlink-event-dispatcher": "^4.2", "shlinkio/shlink-importer": "^5.6", "shlinkio/shlink-installer": "dev-develop#c5a8523 as 9.6", "shlinkio/shlink-ip-geolocation": "^4.3", "shlinkio/shlink-json": "^1.2", "spiral/roadrunner": "^2025.1", "spiral/roadrunner-cli": "^2.7", "spiral/roadrunner-http": "^3.5", "spiral/roadrunner-jobs": "^4.6", "symfony/console": "^7.3", "symfony/filesystem": "^7.3", "symfony/lock": "7.1.6", "symfony/process": "^7.3", "symfony/string": "^7.3"}, "require-dev": {"devizzent/cebe-php-openapi": "^1.1.2", "devster/ubench": "^2.1", "phpstan/phpstan": "^2.1", "phpstan/phpstan-doctrine": "^2.0", "phpstan/phpstan-phpunit": "^2.0.5", "phpstan/phpstan-symfony": "^2.0", "phpunit/php-code-coverage": "^12.0", "phpunit/phpcov": "^11.0", "phpunit/phpunit": "^12.0.10", "roave/security-advisories": "dev-master", "shlinkio/php-coding-standard": "~2.4.2", "shlinkio/shlink-test-utils": "^4.3.1", "symfony/var-dumper": "^7.3", "veewee/composer-run-parallel": "^1.4"}, "conflict": {"symfony/var-exporter": ">=6.3.9,<=6.4.0"}, "autoload": {"psr-4": {"Shlinkio\\Shlink\\CLI\\": "module/CLI/src", "Shlinkio\\Shlink\\Rest\\": "module/Rest/src", "Shlinkio\\Shlink\\Core\\": "module/Core/src"}, "files": ["config/constants.php", "module/Core/functions/array-utils.php", "module/Core/functions/functions.php"]}, "autoload-dev": {"psr-4": {"ShlinkioTest\\Shlink\\CLI\\": "module/CLI/test", "ShlinkioCliTest\\Shlink\\CLI\\": "module/CLI/test-cli", "ShlinkioTest\\Shlink\\Rest\\": "module/Rest/test", "ShlinkioApiTest\\Shlink\\Rest\\": "module/Rest/test-api", "ShlinkioDbTest\\Shlink\\Rest\\": "module/Rest/test-db", "ShlinkioTest\\Shlink\\Core\\": "module/Core/test", "ShlinkioDbTest\\Shlink\\Core\\": "module/Core/test-db", "ShlinkioApiTest\\Shlink\\Core\\": "module/Core/test-api"}, "files": ["config/test/constants.php"]}, "scripts": {"ci": ["@parallel cs stan openapi:validate test:unit:ci test:db:sqlite:ci test:db:postgres test:db:mysql test:db:maria test:db:ms", "@parallel test:api:ci test:cli:ci"], "cs": "phpcs -s", "cs:fix": "phpcbf", "stan": ["@putenv APP_ENV=test", "phpstan analyse"], "test": ["@parallel test:unit test:db", "@parallel test:api test:cli"], "test:unit": ["@putenv COLUMNS=120", "phpunit --order-by=random --testdox --testdox-summary"], "test:unit:ci": ["@putenv XDEBUG_MODE=coverage", "@test:unit --coverage-php=build/coverage-unit.cov"], "test:unit:pretty": ["@putenv XDEBUG_MODE=coverage", "@test:unit --coverage-html build/coverage-unit/coverage-html"], "test:db": "@parallel test:db:sqlite:ci test:db:mysql test:db:maria test:db:postgres test:db:ms", "test:db:sqlite": ["@putenv APP_ENV=test", "phpunit --order-by=random --testdox --testdox-summary -c phpunit-db.xml"], "test:db:sqlite:ci": ["@putenv XDEBUG_MODE=coverage", "@test:db:sqlite --coverage-php build/coverage-db.cov"], "test:db:mysql": ["@putenv DB_DRIVER=mysql", "@test:db:sqlite"], "test:db:maria": ["@putenv DB_DRIVER=maria", "@test:db:sqlite"], "test:db:postgres": ["@putenv DB_DRIVER=postgres", "@test:db:sqlite"], "test:db:ms": ["@putenv DB_DRIVER=mssql", "@test:db:sqlite"], "test:api": "bin/test/run-api-tests.sh", "test:api:sqlite": ["@putenv DB_DRIVER=sqlite", "@test:api"], "test:api:mysql": ["@putenv DB_DRIVER=mysql", "@test:api"], "test:api:maria": ["@putenv DB_DRIVER=maria", "@test:api"], "test:api:mssql": ["@putenv DB_DRIVER=mssql", "@test:api"], "test:api:ci": ["@putenv GENERATE_COVERAGE=yes", "@test:api", "phpcov merge build/coverage-api --php build/coverage-api.cov && rm build/coverage-api/*.cov"], "test:api:pretty": ["@putenv GENERATE_COVERAGE=yes", "@test:api", "phpcov merge build/coverage-api --html build/coverage-api/coverage-html && rm build/coverage-api/*.cov"], "test:cli": "bin/test/run-cli-tests.sh", "test:cli:ci": ["@putenv GENERATE_COVERAGE=yes", "@test:cli", "vendor/bin/phpcov merge build/coverage-cli --php build/coverage-cli.cov && rm build/coverage-cli/*.cov"], "test:cli:pretty": ["@putenv GENERATE_COVERAGE=yes", "@test:cli", "phpcov merge build/coverage-cli --html build/coverage-cli/coverage-html && rm build/coverage-cli/*.cov"], "openapi:validate": "php-openapi validate docs/swagger/swagger.json", "openapi:inline": "php-openapi inline docs/swagger/swagger.json docs/swagger/openapi-inlined.json", "clean:dev": "rm -f data/database.sqlite && rm -f config/params/generated_config.php"}, "config": {"sort-packages": true, "platform-check": false, "allow-plugins": {"composer/package-versions-deprecated": true, "dealerdirect/phpcodesniffer-composer-installer": true, "veewee/composer-run-parallel": true}}}