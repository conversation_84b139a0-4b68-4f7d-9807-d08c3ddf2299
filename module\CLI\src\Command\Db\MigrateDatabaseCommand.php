<?php

declare(strict_types=1);

namespace Shl<PERSON>io\Shlink\CLI\Command\Db;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

class MigrateDatabaseCommand extends AbstractDatabaseCommand
{
    public const string NAME = 'db:migrate';
    public const string DOCTRINE_MIGRATIONS_SCRIPT = 'vendor/doctrine/migrations/bin/doctrine-migrations.php';
    public const string DOCTRINE_MIGRATE_COMMAND = 'migrations:migrate';

    protected function configure(): void
    {
        $this
            ->setName(self::NAME)
            ->setHidden()
            ->setDescription('Runs database migrations, which will ensure the shlink database is up to date.');
    }

    protected function lockedExecute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->writeln('<fg=blue>Migrating database...</>');
        $this->runPhpCommand($output, [self::DOCTRINE_MIGRATIONS_SCRIPT, self::DOCTRINE_MIGRATE_COMMAND]);
        $io->success('Database properly migrated!');

        return self::SUCCESS;
    }
}
