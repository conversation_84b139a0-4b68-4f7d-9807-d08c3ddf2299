{"get": {"deprecated": true, "operationId": "shortUrlQrCode", "tags": ["URL Shortener"], "summary": "[Deprecated] Short URL QR code", "description": "**[Deprecated]** Use an external mechanism to generate QR codes. Shlink dashboard and shlink-web-client provide their own.", "parameters": [{"$ref": "../parameters/shortCode.json"}, {"name": "size", "in": "query", "description": "The size of the image to be returned.", "required": false, "schema": {"type": "integer", "minimum": 50, "maximum": 1000, "default": 300}}, {"name": "format", "in": "query", "description": "The format for the QR code image, being valid values png and svg. Not providing the param or providing any other value will fall back to png.", "required": false, "schema": {"type": "string", "enum": ["png", "svg"], "default": "png"}}, {"name": "margin", "in": "query", "description": "The margin around the QR code image.", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0}}, {"name": "errorCorrection", "in": "query", "description": "The error correction level to apply to the QR code: **[L]**ow, **[M]**edium, **[Q]**uartile or **[H]**igh. See [docs](https://www.qrcode.com/en/about/error_correction.html).", "required": false, "schema": {"type": "string", "enum": ["L", "M", "Q", "H"], "default": "L"}}, {"name": "roundBlockSize", "in": "query", "description": "Allows to disable block size rounding, which might reduce the readability of the QR code, but ensures no extra margin is added.", "required": false, "schema": {"type": "string", "enum": ["true", "false"], "default": "false"}}, {"name": "color", "in": "query", "description": "The QR code foreground color. It should be an hex representation of a color, in 3 or 6 characters, optionally preceded by the \"#\" character.", "required": false, "schema": {"type": "string", "default": "#000000"}}, {"name": "bgColor", "in": "query", "description": "The QR code background color. It should be an hex representation of a color, in 3 or 6 characters, optionally preceded by the \"#\" character.", "required": false, "schema": {"type": "string", "default": "#ffffff"}}, {"name": "logo", "in": "query", "description": "Currently used to disable the logo that was set via configuration options. It may be used in future to dynamically choose from multiple logos.", "required": false, "schema": {"type": "string", "enum": ["disable"]}}], "responses": {"200": {"description": "QR code in PNG format", "content": {"image/png": {"schema": {"type": "string", "format": "binary"}}, "image/svg+xml": {"schema": {"type": "string", "format": "binary"}}}}}}}