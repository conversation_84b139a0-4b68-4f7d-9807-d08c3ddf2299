<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\EventDispatcher\Helper;

use Shl<PERSON>io\Shlink\Common\Middleware\RequestIdMiddleware;
use Shl<PERSON>io\Shlink\EventDispatcher\Util\RequestIdProviderInterface;

readonly class RequestIdProvider implements RequestIdProviderInterface
{
    public function __construct(private RequestIdMiddleware $requestIdMiddleware)
    {
    }

    public function currentRequestId(): string
    {
        return $this->requestIdMiddleware->currentRequestId();
    }
}
