<?php

declare(strict_types=1);

namespace <PERSON><PERSON><PERSON><PERSON>\Shlink\Core\Geolocation;

use S<PERSON>inkio\Shlink\Core\Exception\GeolocationDbUpdateFailedException;

interface GeolocationDbUpdaterInterface
{
    /**
     * @throws GeolocationDbUpdateFailedException
     */
    public function checkDbUpdate(
        GeolocationDownloadProgressHandlerInterface|null $downloadProgressHandler = null,
    ): GeolocationResult;
}
