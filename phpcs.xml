<?xml version="1.0"?>
<ruleset name="Coding standard">
    <description>Coding standard</description>

    <!-- display progress -->
    <arg value="p" />
    <arg name="colors" />

    <!-- inherit rules from: -->
    <rule ref="Shlinkio" />

    <!-- Paths to check -->
    <file>bin</file>
    <file>module</file>
    <file>config</file>
    <file>docker/config</file>
    <file>public/index.php</file>

    <!-- Paths to exclude -->
    <exclude-pattern>config/params/*</exclude-pattern>
</ruleset>
